#!/usr/bin/env python3
"""
Test script to verify the restored EEI workflow integration.

This script tests that the derived log calculations and calculator integration
have been properly restored to the refactored workflow.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_workflow_orchestrator_import():
    """Test that the workflow orchestrator can be imported successfully."""
    try:
        from ui.workflow_orchestration import WorkflowOrchestrator
        print("✅ WorkflowOrchestrator imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import WorkflowOrchestrator: {e}")
        return False

def test_derived_log_method():
    """Test that the derived log calculation method exists."""
    try:
        from ui.workflow_orchestration import WorkflowOrchestrator
        orchestrator = WorkflowOrchestrator()
        
        # Check if method exists
        if hasattr(orchestrator, 'calculate_derived_logs'):
            print("✅ calculate_derived_logs method exists")
            return True
        else:
            print("❌ calculate_derived_logs method not found")
            return False
    except Exception as e:
        print(f"❌ Error testing derived log method: {e}")
        return False

def test_calculator_integration_method():
    """Test that the calculator integration method exists."""
    try:
        from ui.workflow_orchestration import WorkflowOrchestrator
        orchestrator = WorkflowOrchestrator()
        
        # Check if method exists
        if hasattr(orchestrator, 'integrate_calculator'):
            print("✅ integrate_calculator method exists")
            return True
        else:
            print("❌ integrate_calculator method not found")
            return False
    except Exception as e:
        print(f"❌ Error testing calculator integration method: {e}")
        return False

def test_workflow_sequence():
    """Test that the main workflow includes the new steps."""
    try:
        from ui.workflow_orchestration import WorkflowOrchestrator
        import inspect
        
        orchestrator = WorkflowOrchestrator()
        
        # Get the source code of run_eei_analysis method
        source = inspect.getsource(orchestrator.run_eei_analysis)
        
        # Check for the new workflow steps
        has_derived_logs = 'calculate_derived_logs' in source
        has_calculator = 'integrate_calculator' in source
        
        if has_derived_logs and has_calculator:
            print("✅ Workflow sequence includes both derived logs and calculator integration")
            return True
        else:
            print(f"❌ Workflow sequence missing steps: derived_logs={has_derived_logs}, calculator={has_calculator}")
            return False
    except Exception as e:
        print(f"❌ Error testing workflow sequence: {e}")
        return False

def test_log_keywords_availability():
    """Test that required log keywords are available."""
    try:
        from eei_config import LOG_KEYWORDS
        
        required_keywords = ['PHIE', 'SWE', 'PHIT', 'SWT', 'NPHI', 'KDRY', 'KSOLID', 'GDRY', 'GSOLID']
        missing_keywords = []
        
        for keyword in required_keywords:
            if keyword not in LOG_KEYWORDS:
                missing_keywords.append(keyword)
        
        if not missing_keywords:
            print("✅ All required log keywords are available")
            return True
        else:
            print(f"❌ Missing log keywords: {missing_keywords}")
            return False
    except Exception as e:
        print(f"❌ Error testing log keywords: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing EEI Workflow Integration")
    print("=" * 50)
    
    tests = [
        test_workflow_orchestrator_import,
        test_derived_log_method,
        test_calculator_integration_method,
        test_workflow_sequence,
        test_log_keywords_availability
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The workflow integration is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
