#!/usr/bin/env python3
"""
Test script to verify that the get_calculations_for_eei function error is fixed.

This script tests the function call that was causing the error:
"get_calculations_for_eei() takes 0 positional arguments but 1 was given"
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_calculator_interface_import():
    """Test that we can import the calculator interface module."""
    try:
        from ui.calculator_interface import get_calculations_for_eei, CalculatorInterface
        print("✅ Successfully imported calculator interface components")
        return True
    except ImportError as e:
        print(f"❌ Failed to import calculator interface: {e}")
        return False

def test_get_calculations_for_eei_with_parameter():
    """Test calling get_calculations_for_eei with las_files parameter."""
    try:
        from ui.calculator_interface import get_calculations_for_eei
        
        # Create a mock las_files list (empty for testing)
        mock_las_files = []
        
        # This should not raise an error anymore
        result = get_calculations_for_eei(mock_las_files)
        print(f"✅ get_calculations_for_eei(las_files) called successfully, returned: {result}")
        return True
    except TypeError as e:
        if "takes 0 positional arguments but 1 was given" in str(e):
            print(f"❌ Original error still present: {e}")
            return False
        else:
            print(f"❌ Different TypeError occurred: {e}")
            return False
    except Exception as e:
        print(f"⚠️ Other exception occurred (this might be expected): {e}")
        return True  # Other exceptions are OK, we just want to avoid the TypeError

def test_get_calculations_for_eei_without_parameter():
    """Test calling get_calculations_for_eei without parameters."""
    try:
        from ui.calculator_interface import get_calculations_for_eei
        
        # This should also work (backward compatibility)
        result = get_calculations_for_eei()
        print(f"✅ get_calculations_for_eei() called successfully, returned: {result}")
        return True
    except Exception as e:
        print(f"❌ Error calling get_calculations_for_eei() without parameters: {e}")
        return False

def test_calculator_interface_class():
    """Test that we can instantiate the CalculatorInterface class."""
    try:
        from ui.calculator_interface import CalculatorInterface
        
        calc_interface = CalculatorInterface()
        print("✅ Successfully created CalculatorInterface instance")
        
        # Test calling the method with empty las_files
        mock_las_files = []
        result = calc_interface.get_calculations_for_eei(mock_las_files)
        print(f"✅ CalculatorInterface.get_calculations_for_eei() called successfully, returned: {result}")
        return True
    except Exception as e:
        print(f"❌ Error with CalculatorInterface class: {e}")
        return False

def test_legacy_wrapper():
    """Test the legacy wrapper function in the main file."""
    try:
        # Import the main file's wrapper function
        from load_multilas_EEI_XCOR_PLOT_Final import get_calculations_for_eei as legacy_get_calculations
        
        # Create a mock las_files list
        mock_las_files = []
        
        # This should not raise the original error
        result = legacy_get_calculations(mock_las_files)
        print(f"✅ Legacy wrapper get_calculations_for_eei(las_files) called successfully, returned: {result}")
        return True
    except TypeError as e:
        if "takes 0 positional arguments but 1 was given" in str(e):
            print(f"❌ Original error still present in legacy wrapper: {e}")
            return False
        else:
            print(f"❌ Different TypeError in legacy wrapper: {e}")
            return False
    except Exception as e:
        print(f"⚠️ Other exception in legacy wrapper (might be expected): {e}")
        return True

def main():
    """Run all tests."""
    print("="*60)
    print("🧪 TESTING CALCULATOR INTERFACE FIX")
    print("="*60)
    
    tests = [
        ("Import Test", test_calculator_interface_import),
        ("Module Function with Parameter", test_get_calculations_for_eei_with_parameter),
        ("Module Function without Parameter", test_get_calculations_for_eei_without_parameter),
        ("Class Method Test", test_calculator_interface_class),
        ("Legacy Wrapper Test", test_legacy_wrapper),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 40)
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The calculator interface fix is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. The fix may need additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
